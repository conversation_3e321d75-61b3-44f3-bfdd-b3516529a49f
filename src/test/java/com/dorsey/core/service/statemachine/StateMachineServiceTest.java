package com.dorsey.core.service.statemachine;

import com.dorsey.core.dto.statemachine.CaseStatusTransitionDTO;
import com.dorsey.core.model.statemachine.CaseStatusTransition;
import com.dorsey.core.repository.statemachine.CaseStatusTransitionRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StateMachineServiceTest {

    @Mock
    private CaseStatusTransitionRepo caseStatusTransitionRepo;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private StateMachineService stateMachineService;

    private CaseStatusTransition transition1;
    private CaseStatusTransition transition2;
    private CaseStatusTransitionDTO transitionDTO1;
    private CaseStatusTransitionDTO transitionDTO2;

    @BeforeEach
    void setUp() {
        transition1 = CaseStatusTransition.builder()
                .id(1)
                .source("OPEN")
                .target("IN_PROGRESS")
                .build();

        transition2 = CaseStatusTransition.builder()
                .id(2)
                .source("IN_PROGRESS")
                .target("CLOSED")
                .build();

        transitionDTO1 = CaseStatusTransitionDTO.builder()
                .id("1")
                .source("OPEN")
                .target("IN_PROGRESS")
                .build();

        transitionDTO2 = CaseStatusTransitionDTO.builder()
                .id("2")
                .source("IN_PROGRESS")
                .target("CLOSED")
                .build();

        ReflectionTestUtils.setField(stateMachineService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveAllCaseStatusTransitions() {
        // Given
        List<CaseStatusTransition> transitions = Arrays.asList(transition1, transition2);
        when(caseStatusTransitionRepo.findAll()).thenReturn(transitions);
        when(modelMapper.map(transition1, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO1);
        when(modelMapper.map(transition2, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO2);

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.retrieveAllCaseStatusTransitions();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(transitionDTO1));
        assertTrue(result.contains(transitionDTO2));
        verify(caseStatusTransitionRepo).findAll();
        verify(modelMapper).map(transition1, CaseStatusTransitionDTO.class);
        verify(modelMapper).map(transition2, CaseStatusTransitionDTO.class);
    }

    @Test
    void testRetrieveAllCaseStatusTransitionsEmpty() {
        // Given
        when(caseStatusTransitionRepo.findAll()).thenReturn(Arrays.asList());

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.retrieveAllCaseStatusTransitions();

        // Then
        assertTrue(result.isEmpty());
        verify(caseStatusTransitionRepo).findAll();
        verifyNoInteractions(modelMapper);
    }

    @Test
    void testRetrieveAllCaseStatusTransitionsWithNullRepository() {
        // Given
        when(caseStatusTransitionRepo.findAll()).thenReturn(null);

        // When & Then
        assertThrows(NullPointerException.class,
            () -> stateMachineService.retrieveAllCaseStatusTransitions());

        verify(caseStatusTransitionRepo).findAll();
    }

    @Test
    void testUpdateAllCaseStatusTransitions() {
        // Given
        List<CaseStatusTransitionDTO> dtos = Arrays.asList(transitionDTO1, transitionDTO2);
        List<CaseStatusTransition> existingTransitions = Arrays.asList(transition1, transition2);

        // Mock findAll() to return the same list both times (for update logic and final retrieve)
        when(caseStatusTransitionRepo.findAll()).thenReturn(existingTransitions, existingTransitions);
        when(caseStatusTransitionRepo.saveAll(anyList())).thenReturn(existingTransitions);
        when(modelMapper.map(transition1, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO1);
        when(modelMapper.map(transition2, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO2);

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.updateAllCaseStatusTransitions(dtos);

        // Then
        assertEquals(2, result.size());
        verify(caseStatusTransitionRepo, times(2)).findAll(); // Called twice: once for update, once for retrieve
        verify(caseStatusTransitionRepo).saveAll(anyList());
        verify(caseStatusTransitionRepo).deleteAllById(anyList());
    }

    @Test
    void testUpdateAllCaseStatusTransitionsWithNewEntries() {
        // Given
        CaseStatusTransitionDTO newTransitionDTO = CaseStatusTransitionDTO.builder()
                .id("3")
                .source("CLOSED")
                .target("REOPENED")
                .build();

        CaseStatusTransition newTransition = CaseStatusTransition.builder()
                .id(3)
                .source("CLOSED")
                .target("REOPENED")
                .build();

        List<CaseStatusTransitionDTO> dtos = Arrays.asList(transitionDTO1, transitionDTO2, newTransitionDTO);
        List<CaseStatusTransition> existingTransitions = Arrays.asList(transition1, transition2);
        List<CaseStatusTransition> allTransitions = Arrays.asList(transition1, transition2, newTransition);

        // Mock findAll() calls: first for update logic, second for final retrieve
        when(caseStatusTransitionRepo.findAll()).thenReturn(existingTransitions, allTransitions);
        when(caseStatusTransitionRepo.saveAll(anyList())).thenReturn(allTransitions);
        when(modelMapper.map(transition1, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO1);
        when(modelMapper.map(transition2, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO2);
        when(modelMapper.map(newTransition, CaseStatusTransitionDTO.class)).thenReturn(newTransitionDTO);

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.updateAllCaseStatusTransitions(dtos);

        // Then
        assertEquals(3, result.size());
        verify(caseStatusTransitionRepo, times(2)).findAll();
        verify(caseStatusTransitionRepo).saveAll(anyList());
        verify(caseStatusTransitionRepo).deleteAllById(anyList());
    }

    @Test
    void testUpdateAllCaseStatusTransitionsWithDeletions() {
        // Given
        List<CaseStatusTransitionDTO> dtos = Arrays.asList(transitionDTO1); // Only one DTO, should delete transition2
        List<CaseStatusTransition> existingTransitions = Arrays.asList(transition1, transition2);
        List<CaseStatusTransition> remainingTransitions = Arrays.asList(transition1);

        // Mock findAll() calls: first for update logic, second for final retrieve
        when(caseStatusTransitionRepo.findAll()).thenReturn(existingTransitions, remainingTransitions);
        when(caseStatusTransitionRepo.saveAll(anyList())).thenReturn(remainingTransitions);
        when(modelMapper.map(transition1, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO1);

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.updateAllCaseStatusTransitions(dtos);

        // Then
        assertEquals(1, result.size());
        verify(caseStatusTransitionRepo, times(2)).findAll();
        verify(caseStatusTransitionRepo).saveAll(anyList());
        verify(caseStatusTransitionRepo).deleteAllById(Arrays.asList(2)); // Should delete transition2
    }

    @Test
    void testUpdateAllCaseStatusTransitionsWithNullDtos() {
        // When & Then
        assertThrows(IllegalArgumentException.class, 
            () -> stateMachineService.updateAllCaseStatusTransitions(null));
        
        verifyNoInteractions(caseStatusTransitionRepo);
    }

    @Test
    void testUpdateAllCaseStatusTransitionsWithEmptyList() {
        // Given
        List<CaseStatusTransitionDTO> emptyDtos = Arrays.asList();
        List<CaseStatusTransition> existingTransitions = Arrays.asList(transition1, transition2);
        List<CaseStatusTransition> emptyTransitions = Arrays.asList();

        // Mock findAll() calls: first for update logic, second for final retrieve
        when(caseStatusTransitionRepo.findAll()).thenReturn(existingTransitions, emptyTransitions);
        when(caseStatusTransitionRepo.saveAll(anyList())).thenReturn(emptyTransitions);

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.updateAllCaseStatusTransitions(emptyDtos);

        // Then
        assertTrue(result.isEmpty());
        verify(caseStatusTransitionRepo, times(2)).findAll();
        verify(caseStatusTransitionRepo).saveAll(anyList());
        verify(caseStatusTransitionRepo).deleteAllById(Arrays.asList(1, 2)); // Should delete all existing
    }

    @Test
    void testUpdateAllCaseStatusTransitionsWithDirtyEntities() {
        // Given
        CaseStatusTransition dirtyTransition = spy(transition1);
        when(dirtyTransition.isDirty()).thenReturn(true);

        List<CaseStatusTransitionDTO> dtos = Arrays.asList(transitionDTO1);
        List<CaseStatusTransition> existingTransitions = Arrays.asList(dirtyTransition);

        // Mock findAll() calls: first for update logic, second for final retrieve
        when(caseStatusTransitionRepo.findAll()).thenReturn(existingTransitions, Arrays.asList(dirtyTransition));
        when(caseStatusTransitionRepo.saveAll(anyList())).thenReturn(Arrays.asList(dirtyTransition));
        when(modelMapper.map(dirtyTransition, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO1);

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.updateAllCaseStatusTransitions(dtos);

        // Then
        assertEquals(1, result.size());
        verify(dirtyTransition).merge(transitionDTO1);
        verify(dirtyTransition).isDirty();
        verify(caseStatusTransitionRepo, times(2)).findAll();
        verify(caseStatusTransitionRepo).saveAll(anyList());
    }

    @Test
    void testUpdateAllCaseStatusTransitionsWithNoChanges() {
        // Given - entities exist but no changes are made (not dirty)
        CaseStatusTransition cleanTransition = spy(transition1);
        when(cleanTransition.isDirty()).thenReturn(false);

        List<CaseStatusTransitionDTO> dtos = Arrays.asList(transitionDTO1);
        List<CaseStatusTransition> existingTransitions = Arrays.asList(cleanTransition);

        // Mock findAll() calls: first for update logic, second for final retrieve
        when(caseStatusTransitionRepo.findAll()).thenReturn(existingTransitions, existingTransitions);
        when(caseStatusTransitionRepo.saveAll(anyList())).thenReturn(Arrays.asList());
        when(modelMapper.map(cleanTransition, CaseStatusTransitionDTO.class)).thenReturn(transitionDTO1);

        // When
        List<CaseStatusTransitionDTO> result = stateMachineService.updateAllCaseStatusTransitions(dtos);

        // Then
        assertEquals(1, result.size());
        verify(cleanTransition).merge(transitionDTO1);
        verify(cleanTransition).isDirty();
        verify(caseStatusTransitionRepo, times(2)).findAll();
        verify(caseStatusTransitionRepo).saveAll(Arrays.asList()); // Empty list since no dirty entities
        verify(caseStatusTransitionRepo).deleteAllById(anyList());
    }
}
