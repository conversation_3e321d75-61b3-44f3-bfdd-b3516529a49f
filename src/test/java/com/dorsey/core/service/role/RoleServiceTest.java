package com.dorsey.core.service.role;

import com.dorsey.core.dto.roles.RoleCapabilitiesDTO;
import com.dorsey.core.dto.roles.RoleDTO;
import com.dorsey.core.dto.task.RoleWorkflowTaskXrefDTO;
import com.dorsey.core.exception.NotFoundException;
import com.dorsey.core.mapper.role.RoleMapper;
import com.dorsey.core.model.roles.Role;
import com.dorsey.core.model.roles.RoleCapabilitiesXref;
import com.dorsey.core.model.workflow.RoleWorkflowTaskXref;
import com.dorsey.core.repository.role.RoleRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RoleServiceTest {

    @Mock
    private RoleRepo roleRepository;

    @Mock
    private RoleMapper roleMapper;

    @Mock
    private ModelMapper modelMapper;

    @Mock
    private TypeMap<Role, RoleDTO> typeMap;

    @InjectMocks
    private RoleService roleService;

    private Role role;
    private RoleDTO roleDTO;
    private UUID roleId;

    @BeforeEach
    void setUp() {
        roleId = UUID.randomUUID();

        // Create test capabilities
        List<RoleCapabilitiesDTO> capabilities = new ArrayList<>();
        capabilities.add(RoleCapabilitiesDTO.builder()
                .roleId(roleId)
                .component(Arrays.asList("users", "view"))
                .view(true)
                .edit(false)
                .create(false)
                .delete(false)
                .build());

        // Create test tasks
        List<RoleWorkflowTaskXrefDTO> tasks = new ArrayList<>();
        tasks.add(RoleWorkflowTaskXrefDTO.builder()
                .roleId(roleId)
                .taskId(UUID.randomUUID())
                .build());

        role = Role.builder()
                .roleId(roleId)
                .name("Test Role")
                .department("IT")
                .description("Test role description")
                .build();

        roleDTO = RoleDTO.builder()
                .roleId(roleId)
                .name("Test Role")
                .department("IT")
                .description("Test role description")
                .capabilities(capabilities)
                .tasks(tasks)
                .build();

        ReflectionTestUtils.setField(roleService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveRole() {
        // Given
        when(roleRepository.findById(roleId)).thenReturn(Optional.of(role));
        when(modelMapper.map(role, RoleDTO.class)).thenReturn(roleDTO);

        // When
        RoleDTO result = roleService.retrieveRole(roleId);

        // Then
        assertEquals(roleDTO, result);
        verify(roleRepository).findById(roleId);
        verify(modelMapper).map(role, RoleDTO.class);
    }

    @Test
    void testRetrieveRoleNotFound() {
        // Given
        when(roleRepository.findById(roleId)).thenReturn(Optional.empty());
        when(modelMapper.map(null, RoleDTO.class)).thenReturn(null);

        // When
        RoleDTO result = roleService.retrieveRole(roleId);

        // Then
        assertNull(result);
        verify(roleRepository).findById(roleId);
        verify(modelMapper).map(null, RoleDTO.class);
    }

    @Test
    void testRetrieveAllRoles() {
        // Given
        List<Role> roles = Arrays.asList(role);
        when(roleRepository.findAllExceptSystemRoles()).thenReturn(roles);
        when(modelMapper.typeMap(Role.class, RoleDTO.class)).thenReturn(typeMap);
        when(typeMap.addMapping(any(), any())).thenReturn(typeMap);
        when(modelMapper.map(role, RoleDTO.class)).thenReturn(roleDTO);

        // When
        List<RoleDTO> result = roleService.retrieveAllRoles();

        // Then
        assertEquals(1, result.size());
        assertEquals(roleDTO, result.get(0));
        verify(roleRepository).findAllExceptSystemRoles();
        verify(modelMapper, times(2)).typeMap(Role.class, RoleDTO.class);
        verify(typeMap, times(2)).addMapping(any(), any());
        verify(modelMapper).map(role, RoleDTO.class);
    }

    @Test
    void testRetrieveRolesByName() {
        // Given
        List<String> names = Arrays.asList("Test Role", "Another Role");
        List<Role> roles = Arrays.asList(role);
        when(roleRepository.findByNameIsIn(names)).thenReturn(roles);
        when(roleMapper.roleDTO(role)).thenReturn(roleDTO);

        // When
        List<RoleDTO> result = roleService.retrieveRolesByName(names);

        // Then
        assertEquals(1, result.size());
        assertEquals(roleDTO, result.get(0));
        verify(roleRepository).findByNameIsIn(names);
        verify(roleMapper).roleDTO(role);
    }

    @Test
    void testRetrieveRolesByNameEmpty() {
        // Given
        List<String> names = Arrays.asList();
        when(roleRepository.findByNameIsIn(names)).thenReturn(Arrays.asList());

        // When
        List<RoleDTO> result = roleService.retrieveRolesByName(names);

        // Then
        assertTrue(result.isEmpty());
        verify(roleRepository).findByNameIsIn(names);
        verifyNoInteractions(roleMapper);
    }

    @Test
    void testUpdateCapabilities() {
        // Given
        when(roleRepository.findById(roleId)).thenReturn(Optional.of(role));

        // The service creates a new Role entity and saves it, then maps the saved result
        Role savedRole = Role.builder()
                .roleId(roleId)
                .name("Test Role")
                .department("IT")
                .description("Test role description")
                .build();

        when(roleRepository.save(any(Role.class))).thenReturn(savedRole);
        when(modelMapper.map(savedRole, RoleDTO.class)).thenReturn(roleDTO);

        // When
        RoleDTO result = roleService.updateCapabilities(roleDTO);

        // Then
        assertEquals(roleDTO, result);
        verify(roleRepository).findById(roleId);
        verify(roleRepository).save(any(Role.class));
        verify(modelMapper).map(savedRole, RoleDTO.class);
    }

    @Test
    void testUpdateCapabilitiesRoleNotFound() {
        // Given
        when(roleRepository.findById(roleId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(NotFoundException.class, 
            () -> roleService.updateCapabilities(roleDTO));
        
        verify(roleRepository).findById(roleId);
        verify(roleRepository, never()).save(any());
    }

    @Test
    void testUpdateCapabilitiesWithNullRole() {
        // When & Then
        assertThrows(IllegalArgumentException.class, 
            () -> roleService.updateCapabilities(null));
        
        verifyNoInteractions(roleRepository);
    }

    @Test
    void testCheckRoleNamesExistence() {
        // Given
        List<String> names = Arrays.asList("Role1", "Role2");
        when(roleRepository.checkIfNamesExist(names, names.size())).thenReturn(true);

        // When
        Boolean result = roleService.checkRoleNamesExistence(names);

        // Then
        assertTrue(result);
        verify(roleRepository).checkIfNamesExist(names, names.size());
    }

    @Test
    void testCheckRoleNamesExistenceNotFound() {
        // Given
        List<String> names = Arrays.asList("NonExistent1", "NonExistent2");
        when(roleRepository.checkIfNamesExist(names, names.size())).thenReturn(false);

        // When
        Boolean result = roleService.checkRoleNamesExistence(names);

        // Then
        assertFalse(result);
        verify(roleRepository).checkIfNamesExist(names, names.size());
    }

    @Test
    void testCheckRoleNamesExistenceWithNullNames() {
        // When & Then
        assertThrows(IllegalArgumentException.class, 
            () -> roleService.checkRoleNamesExistence(null));
        
        verifyNoInteractions(roleRepository);
    }

    @Test
    void testCheckRoleNamesExistenceWithEmptyNames() {
        // Given
        List<String> emptyNames = Arrays.asList();
        when(roleRepository.checkIfNamesExist(emptyNames, 0)).thenReturn(false);

        // When
        Boolean result = roleService.checkRoleNamesExistence(emptyNames);

        // Then
        assertFalse(result);
        verify(roleRepository).checkIfNamesExist(emptyNames, 0);
    }
}
